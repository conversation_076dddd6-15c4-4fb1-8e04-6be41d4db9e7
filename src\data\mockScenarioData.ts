// Column definitions for the scenario table
export const ScenarioTableColumns = {
  POLICY_YEAR: 'Policy Year',
  END_OF_AGE: 'End of Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_OUTLAY: 'Net Outlay',
  NET_SURRENDER_VALUE: 'Net Surrender Value',
  NET_DEATH_BENEFIT: 'Net Death Benefit'
} as const;

// Column configurations with additional display properties
export const ScenarioTableColumnConfig = [
  { 
    key: 'policyYear',
    header: ScenarioTableColumns.POLICY_YEAR,
    width: 100
  },
  { 
    key: 'endOfAge',
    header: ScenarioTableColumns.END_OF_AGE,
    width: 100
  },
  { 
    key: 'plannedPremium',
    header: ScenarioTableColumns.PLANNED_PREMIUM,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netOutlay',
    header: ScenarioTableColumns.NET_OUTLAY,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netSurrenderValue',
    header: ScenarioTableColumns.NET_SURRENDER_VALUE,
    width: 180,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: ScenarioTableColumns.NET_DEATH_BENEFIT,
    width: 180,
    isCurrency: true
  }
];

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CASH VALUE ANALYSIS TABLE =====
export const CashValueTableColumns = {
  YEAR: 'Year',
  CASH_VALUE: 'Cash Value',
  LOAN_AVAILABLE: 'Loan Available',
  INTEREST_CREDITED: 'Interest Credited',
  WITHDRAWAL_AVAILABLE: 'Withdrawal Available',
  TOTAL_CASH_VALUE: 'Total Cash Value'
} as const;

export const CashValueTableColumnConfig = [
  { 
    key: 'year',
    header: CashValueTableColumns.YEAR,
    width: 80
  },
  { 
    key: 'cashValue',
    header: CashValueTableColumns.CASH_VALUE,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'loanAvailable',
    header: CashValueTableColumns.LOAN_AVAILABLE,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'interestCredited',
    header: CashValueTableColumns.INTEREST_CREDITED,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'withdrawalAvailable',
    header: CashValueTableColumns.WITHDRAWAL_AVAILABLE,
    width: 160,
    isCurrency: true
  },
  { 
    key: 'totalCashValue',
    header: CashValueTableColumns.TOTAL_CASH_VALUE,
    width: 160,
    isCurrency: true
  }
];

export interface CashValueTableData {
  year: number;
  cashValue: number;
  loanAvailable: number;
  interestCredited: number;
  withdrawalAvailable: number;
  totalCashValue: number;
}

// ===== PREMIUM ANALYSIS TABLE =====
export const PremiumAnalysisTableColumns = {
  POLICY_YEAR: 'Policy Year',
  BASE_PREMIUM: 'Base Premium',
  RIDER_PREMIUM: 'Rider Premium',
  TOTAL_PREMIUM: 'Total Premium',
  PREMIUM_TAX: 'Premium Tax',
  NET_PREMIUM: 'Net Premium'
} as const;

export const PremiumAnalysisTableColumnConfig = [
  { 
    key: 'policyYear',
    header: PremiumAnalysisTableColumns.POLICY_YEAR,
    width: 120
  },
  { 
    key: 'basePremium',
    header: PremiumAnalysisTableColumns.BASE_PREMIUM,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'riderPremium',
    header: PremiumAnalysisTableColumns.RIDER_PREMIUM,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'totalPremium',
    header: PremiumAnalysisTableColumns.TOTAL_PREMIUM,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'premiumTax',
    header: PremiumAnalysisTableColumns.PREMIUM_TAX,
    width: 120,
    isCurrency: true
  },
  { 
    key: 'netPremium',
    header: PremiumAnalysisTableColumns.NET_PREMIUM,
    width: 140,
    isCurrency: true
  }
];

export interface PremiumAnalysisTableData {
  policyYear: number;
  basePremium: number;
  riderPremium: number;
  totalPremium: number;
  premiumTax: number;
  netPremium: number;
}

// ===== DEATH BENEFIT ANALYSIS TABLE =====
export const DeathBenefitTableColumns = {
  AGE: 'Age',
  BASE_DEATH_BENEFIT: 'Base Death Benefit',
  RIDER_BENEFIT: 'Rider Benefit',
  TOTAL_DEATH_BENEFIT: 'Total Death Benefit',
  NET_DEATH_BENEFIT: 'Net Death Benefit',
  BENEFIT_RATIO: 'Benefit Ratio'
} as const;

export const DeathBenefitTableColumnConfig = [
  { 
    key: 'age',
    header: DeathBenefitTableColumns.AGE,
    width: 80
  },
  { 
    key: 'baseDeathBenefit',
    header: DeathBenefitTableColumns.BASE_DEATH_BENEFIT,
    width: 160,
    isCurrency: true
  },
  { 
    key: 'riderBenefit',
    header: DeathBenefitTableColumns.RIDER_BENEFIT,
    width: 140,
    isCurrency: true
  },
  { 
    key: 'totalDeathBenefit',
    header: DeathBenefitTableColumns.TOTAL_DEATH_BENEFIT,
    width: 160,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: DeathBenefitTableColumns.NET_DEATH_BENEFIT,
    width: 160,
    isCurrency: true
  },
  { 
    key: 'benefitRatio',
    header: DeathBenefitTableColumns.BENEFIT_RATIO,
    width: 120,
    isPercentage: true
  }
];

export interface DeathBenefitTableData {
  age: number;
  baseDeathBenefit: number;
  riderBenefit: number;
  totalDeathBenefit: number;
  netDeathBenefit: number;
  benefitRatio: number;
}

// ===== POLICY PERFORMANCE TABLE =====
export const PolicyPerformanceTableColumns = {
  YEAR: 'Year',
  ROI_PERCENTAGE: 'ROI %',
  INTERNAL_RATE_RETURN: 'Internal Rate of Return',
  PAYBACK_PERIOD: 'Payback Period',
  BREAKEVEN_YEAR: 'Breakeven Year',
  PERFORMANCE_SCORE: 'Performance Score'
} as const;

export const PolicyPerformanceTableColumnConfig = [
  { 
    key: 'year',
    header: PolicyPerformanceTableColumns.YEAR,
    width: 80
  },
  { 
    key: 'roiPercentage',
    header: PolicyPerformanceTableColumns.ROI_PERCENTAGE,
    width: 100,
    isPercentage: true
  },
  { 
    key: 'internalRateReturn',
    header: PolicyPerformanceTableColumns.INTERNAL_RATE_RETURN,
    width: 180,
    isPercentage: true
  },
  { 
    key: 'paybackPeriod',
    header: PolicyPerformanceTableColumns.PAYBACK_PERIOD,
    width: 140
  },
  { 
    key: 'breakevenYear',
    header: PolicyPerformanceTableColumns.BREAKEVEN_YEAR,
    width: 140
  },
  { 
    key: 'performanceScore',
    header: PolicyPerformanceTableColumns.PERFORMANCE_SCORE,
    width: 140
  }
];

export interface PolicyPerformanceTableData {
  year: number;
  roiPercentage: number;
  internalRateReturn: number;
  paybackPeriod: number;
  breakevenYear: number;
  performanceScore: number;
}

// ===== RISK ASSESSMENT TABLE =====
export const RiskAssessmentTableColumns = {
  AGE: 'Age',
  MORTALITY_RATE: 'Mortality Rate',
  LAPSE_RISK: 'Lapse Risk',
  INTEREST_RATE_RISK: 'Interest Rate Risk',
  MARKET_RISK: 'Market Risk',
  OVERALL_RISK_SCORE: 'Overall Risk Score'
} as const;

export const RiskAssessmentTableColumnConfig = [
  { 
    key: 'age',
    header: RiskAssessmentTableColumns.AGE,
    width: 80
  },
  { 
    key: 'mortalityRate',
    header: RiskAssessmentTableColumns.MORTALITY_RATE,
    width: 140,
    isPercentage: true
  },
  { 
    key: 'lapseRisk',
    header: RiskAssessmentTableColumns.LAPSE_RISK,
    width: 120,
    isPercentage: true
  },
  { 
    key: 'interestRateRisk',
    header: RiskAssessmentTableColumns.INTEREST_RATE_RISK,
    width: 160,
    isPercentage: true
  },
  { 
    key: 'marketRisk',
    header: RiskAssessmentTableColumns.MARKET_RISK,
    width: 120,
    isPercentage: true
  },
  { 
    key: 'overallRiskScore',
    header: RiskAssessmentTableColumns.OVERALL_RISK_SCORE,
    width: 160
  }
];

export interface RiskAssessmentTableData {
  age: number;
  mortalityRate: number;
  lapseRisk: number;
  interestRateRisk: number;
  marketRisk: number;
  overallRiskScore: number;
}

export const generateMockTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Generating table data for scenario:', scenario.id, scenario.name);

  // Generate mock table data for 10 years (2025-2034)
  const tableData: ScenarioTableData[] = [
    {
      policyYear: 1,
      endOfAge: 40,
      plannedPremium: 10000,
      netOutlay: 5000,
      netSurrenderValue: 50000,
      netDeathBenefit: 250000
    },
    {
      policyYear: 2,
      endOfAge: 41,
      plannedPremium: 10000,
      netOutlay: 5500,
      netSurrenderValue: 51000,
      netDeathBenefit: 255000
    },
    {
      policyYear: 3,
      endOfAge: 42,
      plannedPremium: 10000,
      netOutlay: 6000,
      netSurrenderValue: 52000,
      netDeathBenefit: 260000
    },
    {
      policyYear: 4,
      endOfAge: 43,
      plannedPremium: 10000,
      netOutlay: 6500,
      netSurrenderValue: 53000,
      netDeathBenefit: 265000
    },
    {
      policyYear: 5,
      endOfAge: 44,
      plannedPremium: 10000,
      netOutlay: 7000,
      netSurrenderValue: 54000,
      netDeathBenefit: 270000
    },
    {
      policyYear: 6,
      endOfAge: 45,
      plannedPremium: 10000,
      netOutlay: 7500,
      netSurrenderValue: 55000,
      netDeathBenefit: 275000
    },
    {
      policyYear: 7,
      endOfAge: 46,
      plannedPremium: 10000,
      netOutlay: 8000,
      netSurrenderValue: 56000,
      netDeathBenefit: 280000
    },
    {
      policyYear: 8,
      endOfAge: 47,
      plannedPremium: 10000,
      netOutlay: 8500,
      netSurrenderValue: 57000,
      netDeathBenefit: 285000
    },
    {
      policyYear: 9,
      endOfAge: 48,
      plannedPremium: 10000,
      netOutlay: 9000,
      netSurrenderValue: 58000,
      netDeathBenefit: 290000
    },
    {
      policyYear: 10,
      endOfAge: 49,
      plannedPremium: 10000,
      netOutlay: 9500,
      netSurrenderValue: 59000,
      netDeathBenefit: 295000
    }
  ];

  console.log('✅ Generated table data:', tableData.length, 'rows');
  return tableData;
};

// ===== GENERATE MOCK DATA FOR ALL TABLES =====

export const generateCashValueTableData = (): CashValueTableData[] => {
  return [
    { year: 1, cashValue: 46000, loanAvailable: 40900, interestCredited: 2100, withdrawalAvailable: 35800, totalCashValue: 51200 },
    { year: 2, cashValue: 47000, loanAvailable: 41800, interestCredited: 2200, withdrawalAvailable: 36600, totalCashValue: 52400 },
    { year: 3, cashValue: 48000, loanAvailable: 42700, interestCredited: 2300, withdrawalAvailable: 37400, totalCashValue: 53600 },
    { year: 4, cashValue: 49000, loanAvailable: 43600, interestCredited: 2400, withdrawalAvailable: 38200, totalCashValue: 54800 },
    { year: 5, cashValue: 50000, loanAvailable: 44500, interestCredited: 2500, withdrawalAvailable: 39000, totalCashValue: 56000 },
    { year: 6, cashValue: 51000, loanAvailable: 45400, interestCredited: 2600, withdrawalAvailable: 39800, totalCashValue: 57200 },
    { year: 7, cashValue: 52000, loanAvailable: 46300, interestCredited: 2700, withdrawalAvailable: 40600, totalCashValue: 58400 },
    { year: 8, cashValue: 53000, loanAvailable: 47200, interestCredited: 2800, withdrawalAvailable: 41400, totalCashValue: 59600 },
    { year: 9, cashValue: 54000, loanAvailable: 48100, interestCredited: 2900, withdrawalAvailable: 42200, totalCashValue: 60800 },
    { year: 10, cashValue: 55000, loanAvailable: 49000, interestCredited: 3000, withdrawalAvailable: 43000, totalCashValue: 62000 }
  ];
};

export const generatePremiumAnalysisTableData = (): PremiumAnalysisTableData[] => {
  return [
    { policyYear: 1, basePremium: 8200, riderPremium: 2050, totalPremium: 10250, premiumTax: 525, netPremium: 9725 },
    { policyYear: 2, basePremium: 8400, riderPremium: 2100, totalPremium: 10500, premiumTax: 550, netPremium: 9950 },
    { policyYear: 3, basePremium: 8600, riderPremium: 2150, totalPremium: 10750, premiumTax: 575, netPremium: 10175 },
    { policyYear: 4, basePremium: 8800, riderPremium: 2200, totalPremium: 11000, premiumTax: 600, netPremium: 10400 },
    { policyYear: 5, basePremium: 9000, riderPremium: 2250, totalPremium: 11250, premiumTax: 625, netPremium: 10625 },
    { policyYear: 6, basePremium: 9200, riderPremium: 2300, totalPremium: 11500, premiumTax: 650, netPremium: 10850 },
    { policyYear: 7, basePremium: 9400, riderPremium: 2350, totalPremium: 11750, premiumTax: 675, netPremium: 11075 },
    { policyYear: 8, basePremium: 9600, riderPremium: 2400, totalPremium: 12000, premiumTax: 700, netPremium: 11300 },
    { policyYear: 9, basePremium: 9800, riderPremium: 2450, totalPremium: 12250, premiumTax: 725, netPremium: 11525 },
    { policyYear: 10, basePremium: 10000, riderPremium: 2500, totalPremium: 12500, premiumTax: 750, netPremium: 11750 }
  ];
};

export const generateDeathBenefitTableData = (): DeathBenefitTableData[] => {
  return [
    { age: 40, baseDeathBenefit: 200000, riderBenefit: 50000, totalDeathBenefit: 250000, netDeathBenefit: 240000, benefitRatio: 2.5 },
    { age: 41, baseDeathBenefit: 205000, riderBenefit: 51000, totalDeathBenefit: 256000, netDeathBenefit: 245500, benefitRatio: 2.6 },
    { age: 42, baseDeathBenefit: 210000, riderBenefit: 52000, totalDeathBenefit: 262000, netDeathBenefit: 251000, benefitRatio: 2.7 },
    { age: 43, baseDeathBenefit: 215000, riderBenefit: 53000, totalDeathBenefit: 268000, netDeathBenefit: 256500, benefitRatio: 2.8 },
    { age: 44, baseDeathBenefit: 220000, riderBenefit: 54000, totalDeathBenefit: 274000, netDeathBenefit: 262000, benefitRatio: 2.9 },
    { age: 45, baseDeathBenefit: 225000, riderBenefit: 55000, totalDeathBenefit: 280000, netDeathBenefit: 267500, benefitRatio: 3.0 },
    { age: 46, baseDeathBenefit: 230000, riderBenefit: 56000, totalDeathBenefit: 286000, netDeathBenefit: 273000, benefitRatio: 3.1 },
    { age: 47, baseDeathBenefit: 235000, riderBenefit: 57000, totalDeathBenefit: 292000, netDeathBenefit: 278500, benefitRatio: 3.2 },
    { age: 48, baseDeathBenefit: 240000, riderBenefit: 58000, totalDeathBenefit: 298000, netDeathBenefit: 284000, benefitRatio: 3.3 },
    { age: 49, baseDeathBenefit: 245000, riderBenefit: 59000, totalDeathBenefit: 304000, netDeathBenefit: 289500, benefitRatio: 3.4 }
  ];
};

export const generatePolicyPerformanceTableData = (): PolicyPerformanceTableData[] => {
  return [
    { year: 1, roiPercentage: 6.0, internalRateReturn: 4.5, paybackPeriod: 7.8, breakevenYear: 6.1, performanceScore: 77 },
    { year: 2, roiPercentage: 6.5, internalRateReturn: 4.8, paybackPeriod: 7.6, breakevenYear: 6.2, performanceScore: 79 },
    { year: 3, roiPercentage: 7.0, internalRateReturn: 5.1, paybackPeriod: 7.4, breakevenYear: 6.3, performanceScore: 81 },
    { year: 4, roiPercentage: 7.5, internalRateReturn: 5.4, paybackPeriod: 7.2, breakevenYear: 6.4, performanceScore: 83 },
    { year: 5, roiPercentage: 8.0, internalRateReturn: 5.7, paybackPeriod: 7.0, breakevenYear: 6.5, performanceScore: 85 },
    { year: 6, roiPercentage: 8.5, internalRateReturn: 6.0, paybackPeriod: 6.8, breakevenYear: 6.6, performanceScore: 87 },
    { year: 7, roiPercentage: 9.0, internalRateReturn: 6.3, paybackPeriod: 6.6, breakevenYear: 6.7, performanceScore: 89 },
    { year: 8, roiPercentage: 9.5, internalRateReturn: 6.6, paybackPeriod: 6.4, breakevenYear: 6.8, performanceScore: 91 },
    { year: 9, roiPercentage: 10.0, internalRateReturn: 6.9, paybackPeriod: 6.2, breakevenYear: 6.9, performanceScore: 93 },
    { year: 10, roiPercentage: 10.5, internalRateReturn: 7.2, paybackPeriod: 6.0, breakevenYear: 7.0, performanceScore: 95 }
  ];
};

export const generateRiskAssessmentTableData = (): RiskAssessmentTableData[] => {
  return [
    { age: 40, mortalityRate: 0.5, lapseRisk: 2.5, interestRateRisk: 3.0, marketRisk: 4.0, overallRiskScore: 65 },
    { age: 41, mortalityRate: 0.6, lapseRisk: 2.4, interestRateRisk: 3.05, marketRisk: 4.08, overallRiskScore: 67 },
    { age: 42, mortalityRate: 0.7, lapseRisk: 2.3, interestRateRisk: 3.1, marketRisk: 4.16, overallRiskScore: 69 },
    { age: 43, mortalityRate: 0.8, lapseRisk: 2.2, interestRateRisk: 3.15, marketRisk: 4.24, overallRiskScore: 71 },
    { age: 44, mortalityRate: 0.9, lapseRisk: 2.1, interestRateRisk: 3.2, marketRisk: 4.32, overallRiskScore: 73 },
    { age: 45, mortalityRate: 1.0, lapseRisk: 2.0, interestRateRisk: 3.25, marketRisk: 4.4, overallRiskScore: 75 },
    { age: 46, mortalityRate: 1.1, lapseRisk: 1.9, interestRateRisk: 3.3, marketRisk: 4.48, overallRiskScore: 77 },
    { age: 47, mortalityRate: 1.2, lapseRisk: 1.8, interestRateRisk: 3.35, marketRisk: 4.56, overallRiskScore: 79 },
    { age: 48, mortalityRate: 1.3, lapseRisk: 1.7, interestRateRisk: 3.4, marketRisk: 4.64, overallRiskScore: 81 },
    { age: 49, mortalityRate: 1.4, lapseRisk: 1.6, interestRateRisk: 3.45, marketRisk: 4.72, overallRiskScore: 83 }
  ];
};
