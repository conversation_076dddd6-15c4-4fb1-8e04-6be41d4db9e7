// Disclosure Service - Only handles disclosure data fetching
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Types for disclosure data
export interface DisclosureItem {
  DISCLOSURE_ID: number;
  TYPE: string;
  DISCLOSURE: string;
}

export interface DisclosureRequest {
  policyId: string;
  customerId: string;
  policyType: string;
}

export interface DisclosureResponse {
  success: boolean;
  data: DisclosureItem[];
  message?: string;
}

/**
 * Fetch disclosure data from backend API
 * @param requestData - The disclosure request parameters
 * @returns Promise with disclosure response
 */
export const fetchDisclosureData = async (requestData: DisclosureRequest): Promise<DisclosureResponse> => {
  try {
    console.log('🔍 Fetching disclosure data from:', `${API_BASE_URL}/disclosures`);
    console.log('📋 Request data:', requestData);

    // Convert request data to URL parameters
    const params = new URLSearchParams({
      policyId: requestData.policyId,
      customerId: requestData.customerId,
      policyType: requestData.policyType
    });
    
    const url = `${API_BASE_URL}/disclosures?${params.toString()}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('� Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseText = await response.text();
    console.log('📄 Raw response text:', responseText);

    let data;
    try {
      data = responseText ? JSON.parse(responseText) : null;
    } catch (parseError) {
      console.error('❌ Failed to parse JSON:', parseError);
      throw new Error('Invalid JSON response from server');
    }

    if (!data) {
      return {
        success: false,
        data: [],
        message: 'No data received from server'
      };
    }

    // Handle different response formats
    let disclosureArray: DisclosureItem[] = [];

    if (Array.isArray(data)) {
      disclosureArray = data;
    } else if (typeof data === 'object') {
      if (data.data && Array.isArray(data.data)) {
        disclosureArray = data.data;
      } else if (data.DISCLOSURE_ID && data.TYPE && data.DISCLOSURE) {
        disclosureArray = [data];
      } else if (data.disclosure_data && Array.isArray(data.disclosure_data)) {
        disclosureArray = data.disclosure_data;
      }
    }

    console.log('📊 Processed disclosure array:', disclosureArray);

    return {
      success: true,
      data: disclosureArray
    };

  } catch (error) {
    console.error('❌ Error in fetchDisclosureData:', error);
    return {
      success: false,
      data: [],
      message: error instanceof Error ? error.message : 'Failed to fetch disclosure data'
    };
  }
};
